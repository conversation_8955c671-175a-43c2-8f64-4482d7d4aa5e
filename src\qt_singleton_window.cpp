#include "qt_singleton_window.h"
#include "dimension_manager.h"
#include "balloon.hpp"
#include <QDateTime>
#include <QScrollBar>
#include <QFont>
#include <QSizePolicy>
#include <QDir>
#include <QCoreApplication>
#include <QLibraryInfo>
#include <QStandardPaths>
#include <QDebug>
#include <QHeaderView>
#include <QMessageBox>
#include <QInputDialog>
#include <QFile>
#include <QFileInfo>
#include <QStringList>

// Windows API for getting DLL path
#ifdef _WIN32
#include <windows.h>
#endif

// Static member definitions for QtSingletonWindow
std::unique_ptr<QtSingletonWindow> QtSingletonWindow::s_instance = nullptr;
std::mutex QtSingletonWindow::s_mutex;

// Static member definitions for QtApplicationManager
std::mutex QtApplicationManager::s_mutex;

// QtSingletonWindow Implementation
QtSingletonWindow::QtSingletonWindow(QWidget* parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_layout(nullptr)
    , m_titleLabel(nullptr)
    , m_messageArea(nullptr)
    , m_closeButton(nullptr)
    , m_clearButton(nullptr)
    , m_tabWidget(nullptr)
    , m_dimensionTab(nullptr)
    , m_messageTab(nullptr)
    , m_splitter(nullptr)
    , m_dimensionGroupBox(nullptr)
    , m_dimensionTable(nullptr)
    , m_controlGroupBox(nullptr)
    , m_extractDimensionsButton(nullptr)
    , m_refreshDimensionsButton(nullptr)
    , m_createBalloonButton(nullptr)
    , m_dimensionCountLabel(nullptr)
    , m_balloonCountLabel(nullptr)
    , m_dimensionManager(std::make_unique<DimensionManager>(this))
{
    setupUI();
}

QtSingletonWindow::~QtSingletonWindow() = default;

QtSingletonWindow* QtSingletonWindow::getInstance()
{
    std::lock_guard<std::mutex> lock(s_mutex);
    if (!s_instance) {
        // Make sure Qt Application is initialized first
        if (!QtApplicationManager::getInstance().isInitialized()) {
            QtApplicationManager::getInstance().initialize();
        }
        s_instance = std::unique_ptr<QtSingletonWindow>(new QtSingletonWindow());
    }
    return s_instance.get();
}

void QtSingletonWindow::setupUI()
{
    // Set window properties
    setWindowTitle("NX Plugin - Dimension & Balloon Manager");
    setMinimumSize(1000, 700);
    resize(1200, 800);

    // Create central widget
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);

    // Create main layout
    m_layout = new QVBoxLayout(m_centralWidget);

    // Create title label
    m_titleLabel = new QLabel("NX Plugin - Dimension & Balloon Manager", this);
    QFont titleFont = m_titleLabel->font();
    titleFont.setPointSize(14);
    titleFont.setBold(true);
    m_titleLabel->setFont(titleFont);
    m_titleLabel->setAlignment(Qt::AlignCenter);
    m_layout->addWidget(m_titleLabel);

    // Create tab widget
    m_tabWidget = new QTabWidget(this);
    m_layout->addWidget(m_tabWidget);

    // Setup tabs
    setupDimensionTab();
    setupMessageTab();

    // Connect dimension manager signals
    connect(m_dimensionManager.get(), &DimensionManager::dimensionsExtracted,
            this, &QtSingletonWindow::onDimensionsExtracted);
    connect(m_dimensionManager.get(), &DimensionManager::balloonAssigned,
            this, &QtSingletonWindow::onBalloonAssigned);
    connect(m_dimensionManager.get(), &DimensionManager::balloonRemoved,
            this, &QtSingletonWindow::onBalloonRemoved);

    // Add initial message
    addMessage("Qt Singleton Window initialized successfully!");
    addMessage("Use the Dimensions tab to extract and manage dimension information.");
}

void QtSingletonWindow::showWindow()
{
    show();
    raise();
    activateWindow();
}

void QtSingletonWindow::hideWindow()
{
    hide();
}

void QtSingletonWindow::addMessage(const QString& message)
{
    if (!m_messageArea) return;
    
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    QString formattedMessage = QString("[%1] %2").arg(timestamp, message);
    
    m_messageArea->append(formattedMessage);
    
    // Auto-scroll to bottom
    QScrollBar* scrollBar = m_messageArea->verticalScrollBar();
    scrollBar->setValue(scrollBar->maximum());
}

bool QtSingletonWindow::isWindowVisible() const
{
    return isVisible();
}

void QtSingletonWindow::onCloseButtonClicked()
{
    hideWindow();
}

void QtSingletonWindow::onClearButtonClicked()
{
    if (m_messageArea) {
        m_messageArea->clear();
        addMessage("Messages cleared.");
    }
}

void QtSingletonWindow::setupDimensionTab()
{
    // Create dimension tab
    m_dimensionTab = new QWidget();
    m_tabWidget->addTab(m_dimensionTab, "Dimensions");

    // Create main layout for dimension tab
    QVBoxLayout* dimensionLayout = new QVBoxLayout(m_dimensionTab);

    // Create splitter for resizable panels
    m_splitter = new QSplitter(Qt::Vertical, m_dimensionTab);
    dimensionLayout->addWidget(m_splitter);

    // Setup dimension table
    setupDimensionTable();

    // Setup control panel
    setupControlPanel();
}

void QtSingletonWindow::setupMessageTab()
{
    // Create message tab
    m_messageTab = new QWidget();
    m_tabWidget->addTab(m_messageTab, "Messages");

    // Create layout for message tab
    QVBoxLayout* messageLayout = new QVBoxLayout(m_messageTab);

    // Create message area
    m_messageArea = new QTextEdit(m_messageTab);
    m_messageArea->setReadOnly(true);
    m_messageArea->setFont(QFont("Consolas", 10));
    messageLayout->addWidget(m_messageArea);

    // Create buttons layout
    QHBoxLayout* buttonLayout = new QHBoxLayout();

    // Create clear button
    m_clearButton = new QPushButton("Clear Messages", m_messageTab);
    connect(m_clearButton, &QPushButton::clicked, this, &QtSingletonWindow::onClearButtonClicked);
    buttonLayout->addWidget(m_clearButton);

    // Add stretch to push close button to the right
    buttonLayout->addStretch();

    // Create close button
    m_closeButton = new QPushButton("Hide Window", m_messageTab);
    connect(m_closeButton, &QPushButton::clicked, this, &QtSingletonWindow::onCloseButtonClicked);
    buttonLayout->addWidget(m_closeButton);

    messageLayout->addLayout(buttonLayout);
}

void QtSingletonWindow::setupDimensionTable()
{
    // Create dimension group box
    m_dimensionGroupBox = new QGroupBox("Dimension Information", m_dimensionTab);
    m_splitter->addWidget(m_dimensionGroupBox);

    // Create layout for dimension group
    QVBoxLayout* dimensionGroupLayout = new QVBoxLayout(m_dimensionGroupBox);

    // Create dimension table
    m_dimensionTable = new QTableWidget(0, 9, m_dimensionGroupBox); // 9 columns
    dimensionGroupLayout->addWidget(m_dimensionTable);

    // Set table headers
    QStringList headers;
    headers << "Dimension ID" << "Text" << "Nominal Value" << "Measurement Type"
            << "Position (X,Y,Z)" << "Sheet" << "Zone" << "Tolerance" << "Balloon ID";
    m_dimensionTable->setHorizontalHeaderLabels(headers);

    // Configure table properties
    m_dimensionTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_dimensionTable->setSelectionMode(QAbstractItemView::SingleSelection);
    m_dimensionTable->setAlternatingRowColors(true);
    m_dimensionTable->setSortingEnabled(true);

    // Auto-resize columns to content
    m_dimensionTable->horizontalHeader()->setStretchLastSection(true);
    m_dimensionTable->horizontalHeader()->setSectionResizeMode(QHeaderView::ResizeToContents);

    // Connect selection change signal
    connect(m_dimensionTable->selectionModel(), &QItemSelectionModel::selectionChanged,
            this, &QtSingletonWindow::onDimensionSelectionChanged);
}

void QtSingletonWindow::setupControlPanel()
{
    // Create control group box
    m_controlGroupBox = new QGroupBox("Controls", m_dimensionTab);
    m_splitter->addWidget(m_controlGroupBox);

    // Create layout for control group
    QVBoxLayout* controlLayout = new QVBoxLayout(m_controlGroupBox);

    // Create button layout
    QHBoxLayout* buttonLayout = new QHBoxLayout();

    // Create extract dimensions button
    m_extractDimensionsButton = new QPushButton("Extract Dimensions", m_controlGroupBox);
    m_extractDimensionsButton->setToolTip("Extract all dimensions from the current NX part");
    connect(m_extractDimensionsButton, &QPushButton::clicked, this, &QtSingletonWindow::onExtractDimensionsClicked);
    buttonLayout->addWidget(m_extractDimensionsButton);

    // Create refresh button
    m_refreshDimensionsButton = new QPushButton("Refresh", m_controlGroupBox);
    m_refreshDimensionsButton->setToolTip("Refresh dimension information from NX");
    connect(m_refreshDimensionsButton, &QPushButton::clicked, this, &QtSingletonWindow::onRefreshDimensionsClicked);
    buttonLayout->addWidget(m_refreshDimensionsButton);

    // Create balloon button
    m_createBalloonButton = new QPushButton("Create Balloon", m_controlGroupBox);
    m_createBalloonButton->setToolTip("Create a balloon for the selected dimension");
    m_createBalloonButton->setEnabled(false); // Disabled until dimension is selected
    connect(m_createBalloonButton, &QPushButton::clicked, this, &QtSingletonWindow::onCreateBalloonClicked);
    buttonLayout->addWidget(m_createBalloonButton);

    // Create advanced balloon button
    m_createAdvancedBalloonButton = new QPushButton("Advanced Balloon", m_controlGroupBox);
    m_createAdvancedBalloonButton->setToolTip("Create a balloon with custom properties for the selected dimension");
    m_createAdvancedBalloonButton->setEnabled(false); // Disabled until dimension is selected
    connect(m_createAdvancedBalloonButton, &QPushButton::clicked, this, &QtSingletonWindow::onCreateAdvancedBalloonClicked);
    buttonLayout->addWidget(m_createAdvancedBalloonButton);

    buttonLayout->addStretch();
    controlLayout->addLayout(buttonLayout);

    // Create statistics layout
    QHBoxLayout* statsLayout = new QHBoxLayout();

    // Create statistics labels
    m_dimensionCountLabel = new QLabel("Dimensions: 0", m_controlGroupBox);
    m_balloonCountLabel = new QLabel("Balloons: 0", m_controlGroupBox);

    statsLayout->addWidget(m_dimensionCountLabel);
    statsLayout->addWidget(m_balloonCountLabel);
    statsLayout->addStretch();

    controlLayout->addLayout(statsLayout);

    // Set splitter proportions (table gets more space)
    m_splitter->setStretchFactor(0, 3); // Table gets 3/4
    m_splitter->setStretchFactor(1, 1); // Controls get 1/4
}

// Dimension management methods
void QtSingletonWindow::extractDimensions()
{
    if (m_dimensionManager) {
        addMessage("Extracting dimensions from NX...");
        if (m_dimensionManager->extractAllDimensions()) {
            addMessage("Dimension extraction completed successfully.");
            populateDimensionTable();
            updateStatistics();
        } else {
            addMessage("Failed to extract dimensions.");
        }
    }
}

void QtSingletonWindow::refreshDimensionGrid()
{
    populateDimensionTable();
    updateStatistics();
}

void QtSingletonWindow::updateBalloonId(const QString& dimensionId, int balloonId)
{
    int row = findDimensionTableRow(dimensionId);
    if (row >= 0) {
        QTableWidgetItem* balloonItem = new QTableWidgetItem(QString::number(balloonId));
        balloonItem->setTextAlignment(Qt::AlignCenter);
        m_dimensionTable->setItem(row, 8, balloonItem); // Column 8 is Balloon ID
        updateStatistics();
    }
}

void QtSingletonWindow::clearBalloonId(const QString& dimensionId)
{
    int row = findDimensionTableRow(dimensionId);
    if (row >= 0) {
        QTableWidgetItem* balloonItem = new QTableWidgetItem("");
        m_dimensionTable->setItem(row, 8, balloonItem); // Column 8 is Balloon ID
        updateStatistics();
    }
}

// Slot implementations
void QtSingletonWindow::onExtractDimensionsClicked()
{
    extractDimensions();
}

void QtSingletonWindow::onRefreshDimensionsClicked()
{
    if (m_dimensionManager) {
        addMessage("Refreshing dimension information...");
        if (m_dimensionManager->refreshFromNX()) {
            addMessage("Dimension refresh completed successfully.");
            refreshDimensionGrid();
        } else {
            addMessage("Failed to refresh dimensions.");
        }
    }
}

void QtSingletonWindow::onCreateBalloonClicked()
{
    if (!m_dimensionTable || !m_dimensionManager) {
        return;
    }

    int currentRow = m_dimensionTable->currentRow();
    if (currentRow < 0) {
        QMessageBox::warning(this, "No Selection", "Please select a dimension first.");
        return;
    }

    // Get dimension ID from the selected row
    QTableWidgetItem* idItem = m_dimensionTable->item(currentRow, 0);
    if (!idItem) {
        return;
    }

    QString dimensionId = idItem->text();
    auto* dimensionInfo = m_dimensionManager->findDimensionById(dimensionId.toStdString());
    if (!dimensionInfo || !dimensionInfo->nxDimension) {
        QMessageBox::warning(this, "Error", "Could not find dimension information or NX dimension object.");
        return;
    }

    // Check if balloon already exists
    if (dimensionInfo->hasBalloon) {
        QMessageBox::information(this, "Balloon Exists",
                                QString("This dimension already has balloon ID: %1").arg(dimensionInfo->balloonId));
        return;
    }

    // Get next available balloon ID
    int nextBalloonId = m_dimensionManager->getNextAvailableBalloonId();

    // Ask user for balloon ID (with default suggestion)
    bool ok;
    int balloonId = QInputDialog::getInt(this, "Create Balloon",
                                       "Enter Balloon ID:", nextBalloonId, 1, 9999, 1, &ok);
    if (!ok) {
        return; // User cancelled
    }

    // Check if balloon ID is already used
    if (m_dimensionManager->isBalloonIdUsed(balloonId)) {
        QMessageBox::warning(this, "ID Already Used",
                           QString("Balloon ID %1 is already in use. Please choose a different ID.").arg(balloonId));
        return;
    }

    try {
        addMessage(QString("Creating balloon %1 for dimension %2...").arg(balloonId).arg(dimensionId));

        // Create the actual balloon using BalloonFactory with enhanced positioning
        auto balloon = BalloonFactory::createForDimension(dimensionInfo->nxDimension, balloonId);
        if (balloon && balloon->isValid()) {
            // Get the balloon properties to verify positioning
            auto properties = balloon->getProperties();
            addMessage(QString("Balloon positioned at: (%1, %2, %3)")
                      .arg(properties.position.x, 0, 'f', 2)
                      .arg(properties.position.y, 0, 'f', 2)
                      .arg(properties.position.z, 0, 'f', 2));

            // Create the balloon in NX
            auto* nxBalloonObject = balloon->create();
            if (nxBalloonObject) {
                // Successfully created balloon - assign it to dimension
                if (m_dimensionManager->assignBalloonToDimension(dimensionId.toStdString(), balloonId)) {
                    addMessage(QString("Successfully created balloon %1 for dimension %2").arg(balloonId).arg(dimensionId));
                    addMessage(QString("Balloon %1 positioned at middle point of dimension as preferred").arg(balloonId));
                    updateBalloonId(dimensionId, balloonId);

                    // Show success message with positioning info
                    QMessageBox::information(this, "Balloon Created",
                                           QString("Balloon %1 has been successfully created and positioned at the middle point of the selected dimension.\n\nPosition: (%2, %3, %4)")
                                           .arg(balloonId)
                                           .arg(properties.position.x, 0, 'f', 2)
                                           .arg(properties.position.y, 0, 'f', 2)
                                           .arg(properties.position.z, 0, 'f', 2));
                } else {
                    addMessage(QString("Balloon created but failed to update dimension tracking for %1").arg(dimensionId));
                    QMessageBox::warning(this, "Partial Success", "Balloon was created in NX but dimension tracking update failed.");
                }
            } else {
                addMessage(QString("Failed to create balloon object in NX for dimension %1").arg(dimensionId));
                QMessageBox::warning(this, "Creation Failed", "Failed to create the balloon object in NX. Check NX session and part state.");
            }
        } else {
            addMessage(QString("Failed to create balloon factory object for dimension %1").arg(dimensionId));
            QMessageBox::warning(this, "Factory Error", "Failed to create balloon using factory. The dimension may not be suitable for balloon creation.");
        }

    } catch (const std::exception& e) {
        QString errorMsg = QString("Exception while creating balloon: %1").arg(e.what());
        addMessage(errorMsg);
        QMessageBox::critical(this, "Error", errorMsg);
    } catch (...) {
        QString errorMsg = "Unknown exception while creating balloon";
        addMessage(errorMsg);
        QMessageBox::critical(this, "Error", errorMsg);
    }
}

void QtSingletonWindow::onCreateAdvancedBalloonClicked()
{
    if (!m_dimensionTable || !m_dimensionManager) {
        return;
    }

    int currentRow = m_dimensionTable->currentRow();
    if (currentRow < 0) {
        QMessageBox::warning(this, "No Selection", "Please select a dimension first.");
        return;
    }

    // Get dimension ID from the selected row
    QTableWidgetItem* idItem = m_dimensionTable->item(currentRow, 0);
    if (!idItem) {
        return;
    }

    QString dimensionId = idItem->text();
    auto* dimensionInfo = m_dimensionManager->findDimensionById(dimensionId.toStdString());
    if (!dimensionInfo || !dimensionInfo->nxDimension) {
        QMessageBox::warning(this, "Error", "Could not find dimension information or NX dimension object.");
        return;
    }

    // Check if balloon already exists
    if (dimensionInfo->hasBalloon) {
        QMessageBox::information(this, "Balloon Exists",
                                QString("This dimension already has balloon ID: %1").arg(dimensionInfo->balloonId));
        return;
    }

    // Get next available balloon ID
    int nextBalloonId = m_dimensionManager->getNextAvailableBalloonId();

    // Ask user for balloon ID (with default suggestion)
    bool ok;
    int balloonId = QInputDialog::getInt(this, "Create Advanced Balloon",
                                       "Enter Balloon ID:", nextBalloonId, 1, 9999, 1, &ok);
    if (!ok) {
        return; // User cancelled
    }

    // Check if balloon ID is already used
    if (m_dimensionManager->isBalloonIdUsed(balloonId)) {
        QMessageBox::warning(this, "ID Already Used",
                           QString("Balloon ID %1 is already in use. Please choose a different ID.").arg(balloonId));
        return;
    }

    try {
        addMessage(QString("Creating advanced balloon %1 for dimension %2...").arg(balloonId).arg(dimensionId));

        // Create balloon with custom properties using BalloonBuilder
        auto balloon = BalloonFactory::createBalloon(
            BalloonType::PMI_BALLOON,
            BalloonBuilder()
                .setId(balloonId)
                .setText(QString::number(balloonId).toStdString())
                .setPosition(dimensionInfo->annotationOrigin.x + 25,
                           dimensionInfo->annotationOrigin.y + 25,
                           dimensionInfo->annotationOrigin.z)
                .useDefaultPmiSettings()
                .setTextColor("Red")
                .setBorderColor("Blue")
                .setHasLeader(true)
                .setArrowType(ArrowType::FILLED_ARROW)
        );

        if (balloon && balloon->isValid()) {
            // Attach to dimension for proper positioning (cast to PmiBalloon to access specific methods)
            if (auto* pmiBalloon = dynamic_cast<PmiBalloon*>(balloon.get())) {
                pmiBalloon->attachToDimension(dimensionInfo->nxDimension);
            }

            // Get the balloon properties to verify positioning
            auto properties = balloon->getProperties();
            addMessage(QString("Advanced balloon positioned at: (%1, %2, %3)")
                      .arg(properties.position.x, 0, 'f', 2)
                      .arg(properties.position.y, 0, 'f', 2)
                      .arg(properties.position.z, 0, 'f', 2));

            // Create the balloon in NX
            auto* nxBalloonObject = balloon->create();
            if (nxBalloonObject) {
                // Successfully created balloon - assign it to dimension
                if (m_dimensionManager->assignBalloonToDimension(dimensionId.toStdString(), balloonId)) {
                    addMessage(QString("Successfully created advanced balloon %1 for dimension %2").arg(balloonId).arg(dimensionId));
                    addMessage(QString("Advanced balloon %1 created with custom PMI properties").arg(balloonId));
                    updateBalloonId(dimensionId, balloonId);

                    // Show success message with properties info
                    QMessageBox::information(this, "Advanced Balloon Created",
                                           QString("Advanced balloon %1 has been successfully created with custom properties:\n\n"
                                                  "• Position: (%2, %3, %4)\n"
                                                  "• Text Color: Red\n"
                                                  "• Border Color: Blue\n"
                                                  "• Leader: Enabled with filled arrow\n"
                                                  "• Positioned at middle point of dimension")
                                           .arg(balloonId)
                                           .arg(properties.position.x, 0, 'f', 2)
                                           .arg(properties.position.y, 0, 'f', 2)
                                           .arg(properties.position.z, 0, 'f', 2));
                } else {
                    addMessage(QString("Advanced balloon created but failed to update dimension tracking for %1").arg(dimensionId));
                    QMessageBox::warning(this, "Partial Success", "Advanced balloon was created in NX but dimension tracking update failed.");
                }
            } else {
                addMessage(QString("Failed to create advanced balloon object in NX for dimension %1").arg(dimensionId));
                QMessageBox::warning(this, "Creation Failed", "Failed to create the advanced balloon object in NX. Check NX session and part state.");
            }
        } else {
            addMessage(QString("Failed to create advanced balloon factory object for dimension %1").arg(dimensionId));
            QMessageBox::warning(this, "Factory Error", "Failed to create advanced balloon using factory. The dimension may not be suitable for balloon creation.");
        }

    } catch (const std::exception& e) {
        QString errorMsg = QString("Exception while creating advanced balloon: %1").arg(e.what());
        addMessage(errorMsg);
        QMessageBox::critical(this, "Error", errorMsg);
    } catch (...) {
        QString errorMsg = "Unknown exception while creating advanced balloon";
        addMessage(errorMsg);
        QMessageBox::critical(this, "Error", errorMsg);
    }
}

void QtSingletonWindow::onDimensionSelectionChanged()
{
    bool hasSelection = m_dimensionTable && m_dimensionTable->currentRow() >= 0;
    if (m_createBalloonButton) {
        m_createBalloonButton->setEnabled(hasSelection);
    }
    if (m_createAdvancedBalloonButton) {
        m_createAdvancedBalloonButton->setEnabled(hasSelection);
    }
}

// Dimension manager signal slots
void QtSingletonWindow::onDimensionsExtracted(size_t count)
{
    addMessage(QString("Extracted %1 dimensions from NX").arg(count));
    populateDimensionTable();
    updateStatistics();
}

void QtSingletonWindow::onBalloonAssigned(const QString& dimensionId, int balloonId)
{
    addMessage(QString("Balloon %1 assigned to dimension %2").arg(balloonId).arg(dimensionId));
    updateBalloonId(dimensionId, balloonId);
}

void QtSingletonWindow::onBalloonRemoved(const QString& dimensionId)
{
    addMessage(QString("Balloon removed from dimension %1").arg(dimensionId));
    clearBalloonId(dimensionId);
}

// Helper methods
void QtSingletonWindow::populateDimensionTable()
{
    if (!m_dimensionTable || !m_dimensionManager) {
        return;
    }

    const auto& dimensions = m_dimensionManager->getDimensions();

    // Clear existing rows
    m_dimensionTable->setRowCount(0);

    // Add rows for each dimension
    for (size_t i = 0; i < dimensions.size(); ++i) {
        m_dimensionTable->insertRow(static_cast<int>(i));
        updateDimensionTableRow(static_cast<int>(i), dimensions[i]);
    }

    // Resize columns to fit content
    m_dimensionTable->resizeColumnsToContents();
}

void QtSingletonWindow::updateDimensionTableRow(int row, const DimensionInfo& info)
{
    if (!m_dimensionTable || row < 0 || row >= m_dimensionTable->rowCount()) {
        return;
    }

    // Column 0: Dimension ID
    QTableWidgetItem* idItem = new QTableWidgetItem(QString::fromStdString(info.dimensionId));
    idItem->setFlags(idItem->flags() & ~Qt::ItemIsEditable); // Read-only
    m_dimensionTable->setItem(row, 0, idItem);

    // Column 1: Text
    QTableWidgetItem* textItem = new QTableWidgetItem(QString::fromStdString(info.getDisplayText()));
    textItem->setFlags(textItem->flags() & ~Qt::ItemIsEditable);
    m_dimensionTable->setItem(row, 1, textItem);

    // Column 2: Nominal Value
    QTableWidgetItem* valueItem = new QTableWidgetItem(QString::number(info.nominalValue, 'f', 3));
    valueItem->setTextAlignment(Qt::AlignRight | Qt::AlignVCenter);
    valueItem->setFlags(valueItem->flags() & ~Qt::ItemIsEditable);
    m_dimensionTable->setItem(row, 2, valueItem);

    // Column 3: Measurement Type
    QTableWidgetItem* typeItem = new QTableWidgetItem(QString::fromStdString(info.measurementType));
    typeItem->setFlags(typeItem->flags() & ~Qt::ItemIsEditable);
    m_dimensionTable->setItem(row, 3, typeItem);

    // Column 4: Position (X,Y,Z)
    QString positionText = QString("(%1, %2, %3)")
                          .arg(info.annotationOrigin.x, 0, 'f', 2)
                          .arg(info.annotationOrigin.y, 0, 'f', 2)
                          .arg(info.annotationOrigin.z, 0, 'f', 2);
    QTableWidgetItem* posItem = new QTableWidgetItem(positionText);
    posItem->setFlags(posItem->flags() & ~Qt::ItemIsEditable);
    m_dimensionTable->setItem(row, 4, posItem);

    // Column 5: Sheet
    QTableWidgetItem* sheetItem = new QTableWidgetItem(QString::fromStdString(info.sheetName));
    sheetItem->setFlags(sheetItem->flags() & ~Qt::ItemIsEditable);
    m_dimensionTable->setItem(row, 5, sheetItem);

    // Column 6: Zone
    QTableWidgetItem* zoneItem = new QTableWidgetItem(QString::fromStdString(info.zone));
    zoneItem->setFlags(zoneItem->flags() & ~Qt::ItemIsEditable);
    m_dimensionTable->setItem(row, 6, zoneItem);

    // Column 7: Tolerance
    QTableWidgetItem* toleranceItem = new QTableWidgetItem(QString::fromStdString(info.getToleranceDisplayText()));
    toleranceItem->setFlags(toleranceItem->flags() & ~Qt::ItemIsEditable);
    m_dimensionTable->setItem(row, 7, toleranceItem);

    // Column 8: Balloon ID
    QString balloonText = info.hasBalloon ? QString::number(info.balloonId) : "";
    QTableWidgetItem* balloonItem = new QTableWidgetItem(balloonText);
    balloonItem->setTextAlignment(Qt::AlignCenter);
    balloonItem->setFlags(balloonItem->flags() & ~Qt::ItemIsEditable);
    if (info.hasBalloon) {
        balloonItem->setBackground(QColor(144, 238, 144)); // Light green background for assigned balloons
    }
    m_dimensionTable->setItem(row, 8, balloonItem);
}

int QtSingletonWindow::findDimensionTableRow(const QString& dimensionId)
{
    if (!m_dimensionTable) {
        return -1;
    }

    for (int row = 0; row < m_dimensionTable->rowCount(); ++row) {
        QTableWidgetItem* idItem = m_dimensionTable->item(row, 0);
        if (idItem && idItem->text() == dimensionId) {
            return row;
        }
    }
    return -1;
}

void QtSingletonWindow::updateStatistics()
{
    if (!m_dimensionManager) {
        return;
    }

    size_t dimensionCount = m_dimensionManager->getDimensionCount();
    size_t balloonCount = m_dimensionManager->getBalloonCount();

    if (m_dimensionCountLabel) {
        m_dimensionCountLabel->setText(QString("Dimensions: %1").arg(dimensionCount));
    }

    if (m_balloonCountLabel) {
        m_balloonCountLabel->setText(QString("Balloons: %1").arg(balloonCount));
    }
}

// QtApplicationManager Implementation
QtApplicationManager& QtApplicationManager::getInstance()
{
    static QtApplicationManager instance;
    return instance;
}

bool QtApplicationManager::initialize()
{
    std::lock_guard<std::mutex> lock(s_mutex);

    if (m_initialized) {
        return true; // Already initialized
    }

    try {
        // Check if QApplication already exists
        if (QApplication::instance() == nullptr) {
            // Set up Qt plugin paths BEFORE creating QApplication
            setupPluginPaths();

            m_app = std::make_unique<QApplication>(m_argc, m_argv);

            // Set application properties
            m_app->setApplicationName("NX Plugin");
            m_app->setApplicationVersion("1.0");
            m_app->setOrganizationName("NX Plugin Developer");

            // Prevent the application from quitting when the last window closes
            m_app->setQuitOnLastWindowClosed(false);

            // Verify platform plugin is loaded
            if (!verifyPlatformPlugin()) {
                m_initialized = false;
                return false;
            }
        }

        m_initialized = true;
        return true;
    }
    catch (const std::exception& e) {
        // Handle initialization error
        m_initialized = false;
        return false;
    }
}

void QtApplicationManager::cleanup()
{
    std::lock_guard<std::mutex> lock(s_mutex);

    if (!m_initialized) {
        return;
    }

    // Clean up the singleton window first
    QtSingletonWindow::s_instance.reset();

    // Clean up QApplication
    if (m_app) {
        m_app.reset();
    }

    m_initialized = false;
}

bool QtApplicationManager::isInitialized() const
{
    return m_initialized;
}

QApplication* QtApplicationManager::getApplication() const
{
    return m_app.get();
}

void QtApplicationManager::setupPluginPaths()
{
    // Get the directory where our DLL is located
    QString dllDir = getDllDirectory();

    if (!dllDir.isEmpty()) {
        qDebug() << "DLL directory:" << dllDir;

        // Set QT_PLUGIN_PATH environment variable FIRST (before QApplication creation)
        qputenv("QT_PLUGIN_PATH", dllDir.toLocal8Bit());

        // Also set QT_QPA_PLATFORM_PLUGIN_PATH specifically for platform plugins
        QString platformPath = dllDir + "/platforms";
        qputenv("QT_QPA_PLATFORM_PLUGIN_PATH", platformPath.toLocal8Bit());

        // Add the DLL directory to Qt plugin paths
        QCoreApplication::addLibraryPath(dllDir);

        // Also add specific plugin subdirectories
        QStringList pluginDirs = {
            dllDir + "/platforms",
            dllDir + "/styles",
            dllDir + "/imageformats",
            dllDir + "/iconengines",
            dllDir + "/generic",
            dllDir + "/networkinformation",
            dllDir + "/tls"
        };

        for (const QString& pluginDir : pluginDirs) {
            if (QDir(pluginDir).exists()) {
                QCoreApplication::addLibraryPath(pluginDir);
                qDebug() << "Added plugin path:" << pluginDir;
            }
        }

        // Enable Qt debug output for plugin loading
        qputenv("QT_DEBUG_PLUGINS", "1");
        qputenv("QT_LOGGING_RULES", "qt.qpa.plugin.debug=true");
    }
}

bool QtApplicationManager::verifyPlatformPlugin()
{
    // Check if platform plugin is available
    QString dllDir = getDllDirectory();
    QString platformPlugin = dllDir + "/platforms/qwindowsd.dll";

    // For release builds, check for release version
    if (!QFile::exists(platformPlugin)) {
        platformPlugin = dllDir + "/platforms/qwindows.dll";
    }

    if (!QFile::exists(platformPlugin)) {
        qDebug() << "Platform plugin not found at:" << platformPlugin;
        return false;
    }

    qDebug() << "Platform plugin found at:" << platformPlugin;
    return true;
}

QString QtApplicationManager::getDllDirectory()
{
    // Get the path of the current module (our DLL)
    HMODULE hModule = nullptr;

    // Get handle to our DLL
    if (GetModuleHandleExW(GET_MODULE_HANDLE_EX_FLAG_FROM_ADDRESS |
                          GET_MODULE_HANDLE_EX_FLAG_UNCHANGED_REFCOUNT,
                          reinterpret_cast<LPCWSTR>(&QtApplicationManager::getInstance),
                          &hModule)) {

        wchar_t path[MAX_PATH];
        if (GetModuleFileNameW(hModule, path, MAX_PATH)) {
            QString dllPath = QString::fromWCharArray(path);
            QFileInfo fileInfo(dllPath);
            return fileInfo.absolutePath();
        }
    }

    // Fallback: try to get current working directory
    return QDir::currentPath();
}
