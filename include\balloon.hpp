#pragma once

#include "common_header.hpp"
#include <memory>
#include <string>
#include <vector>

// Forward declarations
namespace NXOpen {
    class NXObject;
    namespace Annotations {
        class Dimension;
        class DraftingFcf;
    }
}

// 3D Point structure
struct Point3D {
    double x = 0.0;
    double y = 0.0;
    double z = 0.0;

    Point3D() = default;
    Point3D(double x_, double y_, double z_) : x(x_), y(y_), z(z_) {}
    Point3D(const NXOpen::Point3d& nxPoint) : x(nxPoint.X), y(nxPoint.Y), z(nxPoint.Z) {}

    NXOpen::Point3d toNXPoint() const {
        return NXOpen::Point3d(x, y, z);
    }
};

// Balloon types
enum class BalloonType {
    PMI_BALLOON,
    ID_SYMBOL,
    GENERAL_NOTE
};

// Balloon shape types
enum class BalloonShape {
    CIRCLE,
    SQUARE,
    TRIANGLE,
    HEXAGON,
    DIAMOND
};

// Leader arrow types
enum class ArrowType {
    FILLED_ARROW,
    OPEN_ARROW,
    CLOSED_ARROW,
    DOT,
    NO_ARROW
};

// Text alignment
enum class TextAlignment {
    LEFT,
    CENTER,
    RIGHT
};

// Balloon properties structure
struct BalloonProperties {
    int id = 0;
    std::string text = "";
    std::string title = "";
    std::string category = "User Defined";
    std::string identifier = "User Defined";
    std::string revision = "-";
    Point3D position;
    double size = 15.0;
    double textAngle = 0.0;
    BalloonShape shape = BalloonShape::CIRCLE;
    ArrowType arrowType = ArrowType::FILLED_ARROW;
    TextAlignment alignment = TextAlignment::CENTER;
    std::string textColor = "Black";
    std::string backgroundColor = "White";
    std::string borderColor = "Black";
    double stubSize = 5.0;
    bool hasLeader = true;
    std::vector<std::string> additionalText;
};

// Pure virtual base class for all balloon types
class IBalloon {
public:
    virtual ~IBalloon() = default;

    // Core functionality
    virtual NXOpen::NXObject* create() = 0;
    virtual bool update() = 0;
    virtual bool destroy() = 0;

    // Property accessors
    virtual void setProperties(const BalloonProperties& props) = 0;
    virtual BalloonProperties getProperties() const = 0;

    // Position management
    virtual void setPosition(const Point3D& position) = 0;
    virtual Point3D getPosition() const = 0;

    // Text management
    virtual void setText(const std::string& text) = 0;
    virtual std::string getText() const = 0;

    // Validation
    virtual bool isValid() const = 0;

    // Type information
    virtual BalloonType getType() const = 0;
    virtual std::string getTypeName() const = 0;
};

// Concrete implementation for PMI Balloons
class PmiBalloon : public IBalloon {
public:
    PmiBalloon();
    explicit PmiBalloon(const BalloonProperties& props);
    ~PmiBalloon() override;

    // IBalloon interface implementation
    NXOpen::NXObject* create() override;
    bool update() override;
    bool destroy() override;

    void setProperties(const BalloonProperties& props) override;
    BalloonProperties getProperties() const override;

    void setPosition(const Point3D& position) override;
    Point3D getPosition() const override;

    void setText(const std::string& text) override;
    std::string getText() const override;

    bool isValid() const override;

    BalloonType getType() const override { return BalloonType::PMI_BALLOON; }
    std::string getTypeName() const override { return "PMI Balloon"; }

    // PMI-specific methods
    void attachToDimension(NXOpen::Annotations::Dimension* dimension);
    void attachToFcf(NXOpen::Annotations::DraftingFcf* fcf);
    NXOpen::NXObject* getNXObject() const { return m_nxObject; }

private:
    BalloonProperties m_properties;
    NXOpen::NXObject* m_nxObject = nullptr;
    bool m_isCreated = false;

    // Helper methods
    void setupBuilder(void* builder);
    void configureLeader(void* builder);
    void applyColors(void* builder);
    NXOpen::Annotations::LeaderData::ArrowheadType convertArrowType(ArrowType type) const;
};

// Builder pattern for customizing balloon properties
class BalloonBuilder {
public:
    BalloonBuilder();

    // Fluent interface for building balloon properties
    BalloonBuilder& setId(int id);
    BalloonBuilder& setText(const std::string& text);
    BalloonBuilder& setTitle(const std::string& title);
    BalloonBuilder& setCategory(const std::string& category);
    BalloonBuilder& setIdentifier(const std::string& identifier);
    BalloonBuilder& setRevision(const std::string& revision);
    BalloonBuilder& setPosition(const Point3D& position);
    BalloonBuilder& setPosition(double x, double y, double z);
    BalloonBuilder& setSize(double size);
    BalloonBuilder& setTextAngle(double angle);
    BalloonBuilder& setShape(BalloonShape shape);
    BalloonBuilder& setArrowType(ArrowType arrowType);
    BalloonBuilder& setAlignment(TextAlignment alignment);
    BalloonBuilder& setTextColor(const std::string& color);
    BalloonBuilder& setBackgroundColor(const std::string& color);
    BalloonBuilder& setBorderColor(const std::string& color);
    BalloonBuilder& setStubSize(double size);
    BalloonBuilder& setHasLeader(bool hasLeader);
    BalloonBuilder& addAdditionalText(const std::string& text);

    // Preset configurations
    BalloonBuilder& useDefaultPmiSettings();
    BalloonBuilder& useIdSymbolSettings();
    BalloonBuilder& useHighlightSettings();

    // Build the properties
    BalloonProperties build() const;

    // Reset builder to default state
    BalloonBuilder& reset();

private:
    BalloonProperties m_properties;
};

// Factory pattern for creating different types of balloons
class BalloonFactory {
public:
    // Factory methods
    static std::unique_ptr<IBalloon> createPmiBalloon();
    static std::unique_ptr<IBalloon> createPmiBalloon(const BalloonProperties& props);
    static std::unique_ptr<IBalloon> createIdSymbol();
    static std::unique_ptr<IBalloon> createIdSymbol(const BalloonProperties& props);

    // Factory method with builder
    static std::unique_ptr<IBalloon> createBalloon(BalloonType type, const BalloonBuilder& builder);

    // Convenience methods for common scenarios
    static std::unique_ptr<IBalloon> createForDimension(NXOpen::Annotations::Dimension* dimension, int balloonId);
    static std::unique_ptr<IBalloon> createForFcf(NXOpen::Annotations::DraftingFcf* fcf, int balloonId);
    static std::unique_ptr<IBalloon> createAtPosition(const Point3D& position, const std::string& text);

    // Validation
    static bool isTypeSupported(BalloonType type);
    static std::vector<BalloonType> getSupportedTypes();

    // Position calculation utilities (public for use by balloon implementations)
    static Point3D calculatePositionForDimension(NXOpen::Annotations::Dimension* dimension);
    static Point3D calculatePositionForFcf(NXOpen::Annotations::DraftingFcf* fcf);

private:
    BalloonFactory() = default; // Prevent instantiation

    // Helper methods
    static BalloonProperties getDefaultPropertiesForType(BalloonType type);
};

// Legacy function (will be deprecated)
void CreateBalloonPmiNote(NXOpen::Point3d point1);
