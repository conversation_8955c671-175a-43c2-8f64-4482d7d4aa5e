#pragma once

#include "common_header.hpp"
#include "balloon.hpp"
#include <QString>
#include <QObject>
#include <vector>
#include <memory>
#include <map>

// Forward declarations
namespace NXOpen {
    namespace Annotations {
        class Dimension;
    }
}

// Structure to hold dimension information
struct DimensionInfo {
    // Basic identification
    std::string dimensionId;
    std::string dimensionTag;
    
    // Dimension properties
    std::string text;
    double nominalValue = 0.0;
    std::string measurementType;
    std::string toleranceType;
    double upperTolerance = 0.0;
    double lowerTolerance = 0.0;
    double maximumValue = 0.0;
    double minimumValue = 0.0;
    
    // Position and orientation
    Point3D annotationOrigin;
    Point3D measurementDirection;
    double textAngle = 0.0;
    
    // Drawing context
    std::string sheetName;
    std::string viewName;
    std::string zone;
    
    // Balloon association
    int balloonId = -1;  // -1 means no balloon assigned
    bool hasBalloon = false;
    
    // NX Object reference
    NXOpen::Annotations::Dimension* nxDimension = nullptr;
    
    // Default constructor
    DimensionInfo() = default;
    
    // Constructor with NX dimension
    explicit DimensionInfo(NXOpen::Annotations::Dimension* dimension);
    
    // Helper methods
    bool isValid() const;
    std::string getDisplayText() const;
    std::string getToleranceDisplayText() const;
};

// Class to manage dimension extraction and balloon tracking
class DimensionManager : public QObject {
    Q_OBJECT

public:
    explicit DimensionManager(QObject* parent = nullptr);
    ~DimensionManager() override = default;

    // Main functionality
    bool extractAllDimensions();
    void clearDimensions();
    
    // Dimension access
    const std::vector<DimensionInfo>& getDimensions() const { return m_dimensions; }
    size_t getDimensionCount() const { return m_dimensions.size(); }
    
    // Dimension lookup
    DimensionInfo* findDimensionById(const std::string& dimensionId);
    DimensionInfo* findDimensionByTag(const std::string& dimensionTag);
    DimensionInfo* findDimensionByNXObject(NXOpen::Annotations::Dimension* dimension);
    
    // Balloon management
    bool assignBalloonToDimension(const std::string& dimensionId, int balloonId);
    bool assignBalloonToDimension(NXOpen::Annotations::Dimension* dimension, int balloonId);
    bool removeBalloonFromDimension(const std::string& dimensionId);
    bool removeBalloonFromDimension(NXOpen::Annotations::Dimension* dimension);
    
    // Balloon queries
    std::vector<DimensionInfo*> getDimensionsWithBalloons();
    std::vector<DimensionInfo*> getDimensionsWithoutBalloons();
    int getNextAvailableBalloonId() const;
    bool isBalloonIdUsed(int balloonId) const;
    
    // Statistics
    size_t getBalloonCount() const;
    size_t getUnassignedDimensionCount() const;
    
    // Validation and refresh
    bool validateDimensions();
    bool refreshFromNX();

signals:
    // Emitted when dimensions are extracted or updated
    void dimensionsExtracted(size_t count);
    void dimensionsCleared();
    
    // Emitted when balloon assignments change
    void balloonAssigned(const QString& dimensionId, int balloonId);
    void balloonRemoved(const QString& dimensionId);
    
    // Emitted on errors
    void extractionError(const QString& message);

private:
    // Internal data
    std::vector<DimensionInfo> m_dimensions;
    std::map<std::string, size_t> m_dimensionIdMap;  // Maps dimension ID to index in vector
    std::map<int, std::string> m_balloonIdMap;       // Maps balloon ID to dimension ID
    
    // Helper methods for extraction
    bool extractDimensionFromNX(NXOpen::Annotations::Dimension* nxDimension, DimensionInfo& info);
    bool extractDimensionProperties(NXOpen::Annotations::Dimension* nxDimension, DimensionInfo& info);
    bool extractDimensionPosition(NXOpen::Annotations::Dimension* nxDimension, DimensionInfo& info);
    bool extractDimensionContext(NXOpen::Annotations::Dimension* nxDimension, DimensionInfo& info);
    bool extractToleranceInfo(NXOpen::Annotations::Dimension* nxDimension, DimensionInfo& info);
    
    // Helper methods for ID generation
    std::string generateDimensionId(NXOpen::Annotations::Dimension* dimension) const;
    std::string generateDimensionTag(NXOpen::Annotations::Dimension* dimension) const;
    
    // Internal management
    void updateMappings();
    void clearMappings();
    
    // Error handling
    void logError(const std::string& message) const;
    void logWarning(const std::string& message) const;
    void logInfo(const std::string& message) const;
};

// Utility functions
namespace DimensionUtils {
    // Convert NX measurement types to strings
    std::string measurementTypeToString(NXOpen::Annotations::Dimension* dimension);
    
    // Convert NX tolerance types to strings
    std::string toleranceTypeToString(NXOpen::Annotations::Dimension* dimension);
    
    // Extract text from dimension
    std::string extractDimensionText(NXOpen::Annotations::Dimension* dimension);
    
    // Calculate zone from position (if applicable)
    std::string calculateZone(const Point3D& position);
    
    // Validate dimension object
    bool isValidDimension(NXOpen::Annotations::Dimension* dimension);
}
