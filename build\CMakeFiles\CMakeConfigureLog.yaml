
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.14.10+8b8e13593 for .NET Framework
      Build started 06/03/2025 22:51:41.
      
      Project "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\4.0.1\\CompilerIdC\\CompilerIdC.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdC.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdC.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
      VcpkgTripletSelection:
        Using triplet "x64-windows" from "D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\"
        Using normalized configuration "Release"
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /I"D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\include" /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\lib" /LIBPATH:"D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\4.0.1\\CompilerIdC\\CompilerIdC.exe
      AppLocalFromInstalled:
        pwsh.exe -ExecutionPolicy Bypass -noprofile -File "D:\\dev\\2025\\Canonical\\temp\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\4.0.1\\CompilerIdC\\CompilerIdC.exe" "D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdC.tlog\\CompilerIdC.write.1u.tlog" "Debug\\vcpkg.applocal.log"
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate".
      Done Building Project "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\4.0.1\\CompilerIdC\\CompilerIdC.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:01.31
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        D:/dev/2025/tw_demo/build/CMakeFiles/4.0.1/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.14.10+8b8e13593 for .NET Framework
      Build started 06/03/2025 22:51:43.
      
      Project "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\4.0.1\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      VcpkgTripletSelection:
        Using triplet "x64-windows" from "D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\"
        Using normalized configuration "Release"
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /I"D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\include" /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\lib" /LIBPATH:"D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\4.0.1\\CompilerIdCXX\\CompilerIdCXX.exe
      AppLocalFromInstalled:
        pwsh.exe -ExecutionPolicy Bypass -noprofile -File "D:\\dev\\2025\\Canonical\\temp\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\4.0.1\\CompilerIdCXX\\CompilerIdCXX.exe" "D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.write.1u.tlog" "Debug\\vcpkg.applocal.log"
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\4.0.1\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:01.26
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        D:/dev/2025/tw_demo/build/CMakeFiles/4.0.1/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-gsvv4z"
      binary: "D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-gsvv4z"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-gsvv4z'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_0c661.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.10+8b8e13593 for .NET Framework
        Build started 06/03/2025 22:51:44.
        
        Project "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gsvv4z\\cmTC_0c661.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_0c661.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gsvv4z\\Debug\\".
          Creating directory "cmTC_0c661.dir\\Debug\\cmTC_0c661.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_0c661.dir\\Debug\\cmTC_0c661.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_0c661.dir\\Debug\\cmTC_0c661.tlog\\unsuccessfulbuild".
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Debug"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /I"D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_0c661.dir\\Debug\\\\" /Fd"cmTC_0c661.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35209 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /I"D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_0c661.dir\\Debug\\\\" /Fd"cmTC_0c661.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c(1,1): error C1090: PDB API call failed, error code '5': D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gsvv4z\\cmTC_0c661.dir\\Debug\\vc143.pdb [D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gsvv4z\\cmTC_0c661.vcxproj]
        Done Building Project "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gsvv4z\\cmTC_0c661.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gsvv4z\\cmTC_0c661.vcxproj" (default target) (1) ->
        (ClCompile target) -> 
          C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c(1,1): error C1090: PDB API call failed, error code '5': D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gsvv4z\\cmTC_0c661.dir\\Debug\\vc143.pdb [D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gsvv4z\\cmTC_0c661.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.41
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:56 (try_compile)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Check for working C compiler: C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/cl.exe"
    directories:
      source: "D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-jxzi7m"
      binary: "D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-jxzi7m"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_COMPILER_WORKS"
      cached: true
      stdout: |
        Change Dir: 'D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-jxzi7m'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_04f98.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.10+8b8e13593 for .NET Framework
        Build started 06/03/2025 22:51:45.
        
        Project "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jxzi7m\\cmTC_04f98.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_04f98.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jxzi7m\\Debug\\".
          Creating directory "cmTC_04f98.dir\\Debug\\cmTC_04f98.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_04f98.dir\\Debug\\cmTC_04f98.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_04f98.dir\\Debug\\cmTC_04f98.tlog\\unsuccessfulbuild".
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Debug"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /I"D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_04f98.dir\\Debug\\\\" /Fd"cmTC_04f98.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jxzi7m\\testCCompiler.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35209 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /I"D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_04f98.dir\\Debug\\\\" /Fd"cmTC_04f98.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jxzi7m\\testCCompiler.c"
          testCCompiler.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jxzi7m\\Debug\\cmTC_04f98.exe" /INCREMENTAL /ILK:"cmTC_04f98.dir\\Debug\\cmTC_04f98.ilk" /NOLOGO /LIBPATH:"D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\debug\\lib" /LIBPATH:"D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\debug\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\debug\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-jxzi7m/Debug/cmTC_04f98.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-jxzi7m/Debug/cmTC_04f98.lib" /MACHINE:X64  /machine:x64 cmTC_04f98.dir\\Debug\\testCCompiler.obj
          cmTC_04f98.vcxproj -> D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jxzi7m\\Debug\\cmTC_04f98.exe
        AppLocalFromInstalled:
          pwsh.exe -ExecutionPolicy Bypass -noprofile -File "D:\\dev\\2025\\Canonical\\temp\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jxzi7m\\Debug\\cmTC_04f98.exe" "D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\debug\\bin" "cmTC_04f98.dir\\Debug\\cmTC_04f98.tlog\\cmTC_04f98.write.1u.tlog" "cmTC_04f98.dir\\Debug\\vcpkg.applocal.log"
        FinalizeBuildStatus:
          Deleting file "cmTC_04f98.dir\\Debug\\cmTC_04f98.tlog\\unsuccessfulbuild".
          Touching "cmTC_04f98.dir\\Debug\\cmTC_04f98.tlog\\cmTC_04f98.lastbuildstate".
        Done Building Project "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jxzi7m\\cmTC_04f98.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:01.29
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-4qhrpw"
      binary: "D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-4qhrpw"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-4qhrpw'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_8bdca.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.10+8b8e13593 for .NET Framework
        Build started 06/03/2025 22:51:47.
        
        Project "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4qhrpw\\cmTC_8bdca.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_8bdca.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4qhrpw\\Debug\\".
          Creating directory "cmTC_8bdca.dir\\Debug\\cmTC_8bdca.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_8bdca.dir\\Debug\\cmTC_8bdca.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_8bdca.dir\\Debug\\cmTC_8bdca.tlog\\unsuccessfulbuild".
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Debug"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /I"D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_8bdca.dir\\Debug\\\\" /Fd"cmTC_8bdca.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35209 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /I"D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_8bdca.dir\\Debug\\\\" /Fd"cmTC_8bdca.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4qhrpw\\Debug\\cmTC_8bdca.exe" /INCREMENTAL /ILK:"cmTC_8bdca.dir\\Debug\\cmTC_8bdca.ilk" /NOLOGO /LIBPATH:"D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\debug\\lib" /LIBPATH:"D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\debug\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\debug\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-4qhrpw/Debug/cmTC_8bdca.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-4qhrpw/Debug/cmTC_8bdca.lib" /MACHINE:X64  /machine:x64 cmTC_8bdca.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_8bdca.vcxproj -> D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4qhrpw\\Debug\\cmTC_8bdca.exe
        AppLocalFromInstalled:
          pwsh.exe -ExecutionPolicy Bypass -noprofile -File "D:\\dev\\2025\\Canonical\\temp\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4qhrpw\\Debug\\cmTC_8bdca.exe" "D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\debug\\bin" "cmTC_8bdca.dir\\Debug\\cmTC_8bdca.tlog\\cmTC_8bdca.write.1u.tlog" "cmTC_8bdca.dir\\Debug\\vcpkg.applocal.log"
        FinalizeBuildStatus:
          Deleting file "cmTC_8bdca.dir\\Debug\\cmTC_8bdca.tlog\\unsuccessfulbuild".
          Touching "cmTC_8bdca.dir\\Debug\\cmTC_8bdca.tlog\\cmTC_8bdca.lastbuildstate".
        Done Building Project "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4qhrpw\\cmTC_8bdca.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:01.31
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:264 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35209.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindThreads.cmake:97 (check_c_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake:78 (find_package)"
      - "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6/Qt6Dependencies.cmake:34 (_qt_internal_find_third_party_dependencies)"
      - "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake:162 (include)"
      - "CMakeLists.txt:9 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-fiwr2z"
      binary: "D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-fiwr2z"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6;D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-fiwr2z'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_cf78c.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.10+8b8e13593 for .NET Framework
        Build started 06/03/2025 22:51:49.
        
        Project "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fiwr2z\\cmTC_cf78c.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_cf78c.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fiwr2z\\Debug\\".
          Creating directory "cmTC_cf78c.dir\\Debug\\cmTC_cf78c.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_cf78c.dir\\Debug\\cmTC_cf78c.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_cf78c.dir\\Debug\\cmTC_cf78c.tlog\\unsuccessfulbuild".
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Debug"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /I"D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_cf78c.dir\\Debug\\\\" /Fd"cmTC_cf78c.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fiwr2z\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35209 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /I"D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_cf78c.dir\\Debug\\\\" /Fd"cmTC_cf78c.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fiwr2z\\src.c"
          src.c
        D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fiwr2z\\src.c(1,10): error C1083: Cannot open include file: 'pthread.h': No such file or directory [D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fiwr2z\\cmTC_cf78c.vcxproj]
        Done Building Project "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fiwr2z\\cmTC_cf78c.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fiwr2z\\cmTC_cf78c.vcxproj" (default target) (1) ->
        (ClCompile target) -> 
          D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fiwr2z\\src.c(1,10): error C1083: Cannot open include file: 'pthread.h': No such file or directory [D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fiwr2z\\cmTC_cf78c.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.39
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CheckLibraryExists.cmake:112 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindThreads.cmake:112 (check_library_exists)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake:78 (find_package)"
      - "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6/Qt6Dependencies.cmake:34 (_qt_internal_find_third_party_dependencies)"
      - "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake:162 (include)"
      - "CMakeLists.txt:9 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-om4f55"
      binary: "D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-om4f55"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6;D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-om4f55'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_33c11.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.10+8b8e13593 for .NET Framework
        Build started 06/03/2025 22:51:49.
        
        Project "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-om4f55\\cmTC_33c11.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_33c11.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-om4f55\\Debug\\".
          Creating directory "cmTC_33c11.dir\\Debug\\cmTC_33c11.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_33c11.dir\\Debug\\cmTC_33c11.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_33c11.dir\\Debug\\cmTC_33c11.tlog\\unsuccessfulbuild".
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Debug"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /I"D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_33c11.dir\\Debug\\\\" /Fd"cmTC_33c11.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-om4f55\\CheckFunctionExists.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35209 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /I"D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_33c11.dir\\Debug\\\\" /Fd"cmTC_33c11.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-om4f55\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-om4f55\\Debug\\cmTC_33c11.exe" /INCREMENTAL /ILK:"cmTC_33c11.dir\\Debug\\cmTC_33c11.ilk" /NOLOGO /LIBPATH:"D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\debug\\lib" /LIBPATH:"D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\debug\\lib\\manual-link" pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\debug\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-om4f55/Debug/cmTC_33c11.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-om4f55/Debug/cmTC_33c11.lib" /MACHINE:X64  /machine:x64 cmTC_33c11.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: cannot open file 'pthreads.lib' [D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-om4f55\\cmTC_33c11.vcxproj]
        Done Building Project "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-om4f55\\cmTC_33c11.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-om4f55\\cmTC_33c11.vcxproj" (default target) (1) ->
        (Link target) -> 
          LINK : fatal error LNK1104: cannot open file 'pthreads.lib' [D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-om4f55\\cmTC_33c11.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.49
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CheckLibraryExists.cmake:112 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindThreads.cmake:112 (check_library_exists)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake:78 (find_package)"
      - "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6/Qt6Dependencies.cmake:34 (_qt_internal_find_third_party_dependencies)"
      - "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake:162 (include)"
      - "CMakeLists.txt:9 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-mdyd7o"
      binary: "D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-mdyd7o"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6;D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-mdyd7o'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_0dd9d.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.10+8b8e13593 for .NET Framework
        Build started 06/03/2025 22:51:50.
        
        Project "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-mdyd7o\\cmTC_0dd9d.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_0dd9d.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-mdyd7o\\Debug\\".
          Creating directory "cmTC_0dd9d.dir\\Debug\\cmTC_0dd9d.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_0dd9d.dir\\Debug\\cmTC_0dd9d.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_0dd9d.dir\\Debug\\cmTC_0dd9d.tlog\\unsuccessfulbuild".
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Debug"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /I"D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_0dd9d.dir\\Debug\\\\" /Fd"cmTC_0dd9d.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-mdyd7o\\CheckFunctionExists.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35209 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /I"D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_0dd9d.dir\\Debug\\\\" /Fd"cmTC_0dd9d.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-mdyd7o\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-mdyd7o\\Debug\\cmTC_0dd9d.exe" /INCREMENTAL /ILK:"cmTC_0dd9d.dir\\Debug\\cmTC_0dd9d.ilk" /NOLOGO /LIBPATH:"D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\debug\\lib" /LIBPATH:"D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\debug\\lib\\manual-link" pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\debug\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-mdyd7o/Debug/cmTC_0dd9d.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-mdyd7o/Debug/cmTC_0dd9d.lib" /MACHINE:X64  /machine:x64 cmTC_0dd9d.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: cannot open file 'pthread.lib' [D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-mdyd7o\\cmTC_0dd9d.vcxproj]
        Done Building Project "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-mdyd7o\\cmTC_0dd9d.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-mdyd7o\\cmTC_0dd9d.vcxproj" (default target) (1) ->
        (Link target) -> 
          LINK : fatal error LNK1104: cannot open file 'pthread.lib' [D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-mdyd7o\\cmTC_0dd9d.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.58
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CheckCXXSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6/FindWrapAtomic.cmake:36 (check_cxx_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake:78 (find_package)"
      - "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake:45 (include)"
      - "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake:212 (find_package)"
      - "CMakeLists.txt:10 (find_package)"
    checks:
      - "Performing Test HAVE_STDATOMIC"
    directories:
      source: "D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-ag6kiy"
      binary: "D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-ag6kiy"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6;D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin;D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6;D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "HAVE_STDATOMIC"
      cached: true
      stdout: |
        Change Dir: 'D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-ag6kiy'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_6a396.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.10+8b8e13593 for .NET Framework
        Build started 06/03/2025 22:51:51.
        
        Project "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ag6kiy\\cmTC_6a396.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_6a396.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ag6kiy\\Debug\\".
          Creating directory "cmTC_6a396.dir\\Debug\\cmTC_6a396.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_6a396.dir\\Debug\\cmTC_6a396.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_6a396.dir\\Debug\\cmTC_6a396.tlog\\unsuccessfulbuild".
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Debug"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /I"D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_STDATOMIC /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_6a396.dir\\Debug\\\\" /Fd"cmTC_6a396.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ag6kiy\\src.cxx"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35209 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /I"D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_STDATOMIC /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_6a396.dir\\Debug\\\\" /Fd"cmTC_6a396.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ag6kiy\\src.cxx"
          src.cxx
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ag6kiy\\Debug\\cmTC_6a396.exe" /INCREMENTAL /ILK:"cmTC_6a396.dir\\Debug\\cmTC_6a396.ilk" /NOLOGO /LIBPATH:"D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\debug\\lib" /LIBPATH:"D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\debug\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\debug\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-ag6kiy/Debug/cmTC_6a396.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/dev/2025/tw_demo/build/CMakeFiles/CMakeScratch/TryCompile-ag6kiy/Debug/cmTC_6a396.lib" /MACHINE:X64  /machine:x64 cmTC_6a396.dir\\Debug\\src.obj
          cmTC_6a396.vcxproj -> D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ag6kiy\\Debug\\cmTC_6a396.exe
        AppLocalFromInstalled:
          pwsh.exe -ExecutionPolicy Bypass -noprofile -File "D:\\dev\\2025\\Canonical\\temp\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ag6kiy\\Debug\\cmTC_6a396.exe" "D:\\dev\\2025\\Canonical\\temp\\vcpkg\\installed\\x64-windows\\debug\\bin" "cmTC_6a396.dir\\Debug\\cmTC_6a396.tlog\\cmTC_6a396.write.1u.tlog" "cmTC_6a396.dir\\Debug\\vcpkg.applocal.log"
        FinalizeBuildStatus:
          Deleting file "cmTC_6a396.dir\\Debug\\cmTC_6a396.tlog\\unsuccessfulbuild".
          Touching "cmTC_6a396.dir\\Debug\\cmTC_6a396.tlog\\cmTC_6a396.lastbuildstate".
        Done Building Project "D:\\dev\\2025\\tw_demo\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ag6kiy\\cmTC_6a396.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:01.38
        
      exitCode: 0
...
