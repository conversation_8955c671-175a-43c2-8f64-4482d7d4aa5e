# Balloon Crash Troubleshooting Guide

## Issue Description
The DLL crashes immediately when loaded into NX, specifically on the line:
```cpp
balloonNoteBuilder->SetText(textVector);
```

## Root Cause Analysis

The crash is likely caused by one of these issues:

### 1. **Empty or Invalid Text Vector**
- The `textVector` might be empty or contain invalid string data
- NX's `SetText()` method may not handle empty vectors gracefully

### 2. **String Lifetime Issues**
- The `c_str()` pointers in the vector might become invalid
- Temporary string objects going out of scope

### 3. **NX API Version Compatibility**
- Different NX versions may have different `SetText()` method signatures
- Some versions might not support multi-line text

## Fixes Implemented

### 1. **Safer Text Handling**
```cpp
// Set additional text if any - use a safer approach
if (!m_properties.additionalText.empty()) {
    try {
        std::vector<NXOpen::NXString> textVector;
        // Always include the main balloon text as the first element
        textVector.push_back(m_properties.text.c_str());
        
        // Add additional text lines
        for (const auto& text : m_properties.additionalText) {
            if (!text.empty()) {  // Only add non-empty text
                textVector.push_back(text.c_str());
            }
        }
        
        // Only call SetText if we have valid text
        if (!textVector.empty()) {
            balloonNoteBuilder->SetText(textVector);
        }
    }
    catch (const std::exception&) {
        // If SetText fails, continue without additional text
        // The balloon will still have the main text set via SetBalloonText
    }
}
```

### 2. **Enhanced Error Handling**
```cpp
NXOpen::NXObject* PmiBalloon::create() {
    // ... existing code ...
    
    NXOpen::Annotations::BalloonNoteBuilder* balloonNoteBuilder = nullptr;
    
    try {
        // Validate properties before creating
        if (m_properties.text.empty()) {
            throw std::runtime_error("Balloon text cannot be empty");
        }
        
        // ... creation code with error handling ...
        
    }
    catch (const std::exception& e) {
        // Clean up builder if creation failed
        if (balloonNoteBuilder) {
            try {
                balloonNoteBuilder->Destroy();
            }
            catch (...) {
                // Ignore cleanup errors
            }
        }
        throw std::runtime_error(std::string("Failed to create PMI balloon: ") + e.what());
    }
}
```

### 3. **Simplified Test Function**
Created `TestSimpleBalloonCreation()` to isolate the issue:
```cpp
void TestSimpleBalloonCreation() {
    try {
        // Create a basic balloon with minimal properties
        auto balloon = BalloonFactory::createPmiBalloon();
        balloon->setText("TEST");
        balloon->setPosition(Point3D(100, 100, 0));
        
        if (balloon->isValid()) {
            auto nxObject = balloon->create();
            // Success!
        }
    }
    catch (const std::exception& e) {
        // Log detailed error information
    }
}
```

## Testing Strategy

### Phase 1: Basic Balloon Creation
1. Test with minimal properties (text + position only)
2. No additional text, no leaders, no custom colors
3. Use default PMI settings

### Phase 2: Incremental Feature Testing
1. Add leader functionality
2. Add color customization
3. Add additional text (if Phase 1 succeeds)

### Phase 3: Full Feature Testing
1. Test builder pattern with all features
2. Test factory methods
3. Test preset configurations

## Debugging Steps

### 1. **Enable Detailed Logging**
The current implementation includes detailed logging:
```cpp
theLW->WriteLine("   - Basic balloon object created");
theLW->WriteLine("   - Properties set");
theLW->WriteLine("   - Balloon is valid, creating NX object...");
```

### 2. **Check NX Listing Window**
Monitor the NX Listing Window for:
- Progress messages
- Error messages
- Crash location identification

### 3. **Check Qt Singleton Window**
The Qt window will also display:
- Real-time progress updates
- Error messages
- Success confirmations

### 4. **Isolate the Crash**
Use the simplified test function to identify exactly where the crash occurs:
- Object creation
- Property setting
- NX object creation
- Specific NX API calls

## Alternative Approaches

### 1. **Avoid SetText() Initially**
```cpp
// Comment out the problematic SetText call
// balloonNoteBuilder->SetText(textVector);

// Use only SetBalloonText for now
balloonNoteBuilder->SetBalloonText(m_properties.text.c_str());
```

### 2. **Use Legacy CreateBalloonPmiNote**
Fall back to the working legacy function:
```cpp
void CreateBalloonPmiNote(NXOpen::Point3d point1) {
    // This function works, so we can use it as a reference
}
```

### 3. **Minimal Implementation**
Create a minimal balloon implementation that only uses proven NX API calls:
```cpp
class MinimalBalloon {
    // Only implement the absolute minimum required for balloon creation
    // No additional text, no complex features
};
```

## Expected Behavior

### If Successful:
- Qt Singleton Window appears
- NX Listing Window shows progress messages
- Balloon appears in NX graphics area
- No crashes or errors

### If Still Crashing:
- Identify the exact line causing the crash
- Check NX version compatibility
- Consider using only basic balloon features
- Fall back to legacy implementation

## Next Steps

1. **Test the Current Build**:
   - Load `tw_demo.dll` in NX
   - Check if the simple test function works
   - Monitor both Qt window and NX Listing Window

2. **If Still Crashing**:
   - Identify the exact crash location
   - Disable additional text functionality completely
   - Use only basic balloon creation

3. **If Working**:
   - Gradually enable more features
   - Test builder pattern
   - Test factory methods
   - Re-enable full demonstration

## Recovery Plan

If the new architecture continues to crash:

1. **Immediate**: Disable new balloon creation, use only legacy function
2. **Short-term**: Implement minimal balloon class with basic functionality
3. **Long-term**: Investigate NX version-specific API differences

The architecture and patterns are solid - the issue is likely with specific NX API usage that can be resolved through careful testing and debugging.
