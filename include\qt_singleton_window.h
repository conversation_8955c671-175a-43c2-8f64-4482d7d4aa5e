#pragma once

#include <QMainWindow>
#include <QApplication>
#include <QLabel>
#include <QPushButton>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QWidget>
#include <QTextEdit>
#include <QTableWidget>
#include <QTableWidgetItem>
#include <QTabWidget>
#include <QGroupBox>
#include <QSplitter>
#include <memory>
#include <mutex>

// Forward declarations
class DimensionManager;
struct DimensionInfo;

class QtSingletonWindow : public QMainWindow
{
    Q_OBJECT

    // Allow QtApplicationManager to access private members for cleanup
    friend class QtApplicationManager;

public:
    // Singleton access method
    static QtSingletonWindow* getInstance();
    
    // Show the window
    void showWindow();
    
    // Hide the window
    void hideWindow();
    
    // Add a message to the window
    void addMessage(const QString& message);

    // Check if window is visible
    bool isWindowVisible() const;

    // Dimension management
    void extractDimensions();
    void refreshDimensionGrid();
    void updateBalloonId(const QString& dimensionId, int balloonId);
    void clearBalloonId(const QString& dimensionId);

    // Destructor (public for std::unique_ptr)
    ~QtSingletonWindow() override;

private slots:
    void onCloseButtonClicked();
    void onClearButtonClicked();
    void onExtractDimensionsClicked();
    void onRefreshDimensionsClicked();
    void onCreateBalloonClicked();
    void onCreateAdvancedBalloonClicked();
    void onDimensionSelectionChanged();

    // Dimension manager slots
    void onDimensionsExtracted(size_t count);
    void onBalloonAssigned(const QString& dimensionId, int balloonId);
    void onBalloonRemoved(const QString& dimensionId);

private:
    // Private constructor for singleton
    QtSingletonWindow(QWidget* parent = nullptr);
    
    // Delete copy constructor and assignment operator
    QtSingletonWindow(const QtSingletonWindow&) = delete;
    QtSingletonWindow& operator=(const QtSingletonWindow&) = delete;
    
    // Setup UI
    void setupUI();
    void setupDimensionTab();
    void setupMessageTab();
    void setupDimensionTable();
    void setupControlPanel();

    // Helper methods
    void populateDimensionTable();
    void updateDimensionTableRow(int row, const DimensionInfo& info);
    int findDimensionTableRow(const QString& dimensionId);
    void updateStatistics();

    // Static instance
    static std::unique_ptr<QtSingletonWindow> s_instance;
    static std::mutex s_mutex;
    
    // UI components
    QWidget* m_centralWidget;
    QVBoxLayout* m_layout;
    QLabel* m_titleLabel;
    QTextEdit* m_messageArea;
    QPushButton* m_closeButton;
    QPushButton* m_clearButton;

    // Dimension management UI
    QTabWidget* m_tabWidget;
    QWidget* m_dimensionTab;
    QWidget* m_messageTab;
    QSplitter* m_splitter;
    QGroupBox* m_dimensionGroupBox;
    QTableWidget* m_dimensionTable;
    QGroupBox* m_controlGroupBox;
    QPushButton* m_extractDimensionsButton;
    QPushButton* m_refreshDimensionsButton;
    QPushButton* m_createBalloonButton;
    QPushButton* m_createAdvancedBalloonButton;
    QLabel* m_dimensionCountLabel;
    QLabel* m_balloonCountLabel;

    // Dimension manager
    std::unique_ptr<DimensionManager> m_dimensionManager;
};

// Qt Application Manager - handles QApplication lifecycle
class QtApplicationManager
{
public:
    // Get the singleton instance
    static QtApplicationManager& getInstance();
    
    // Initialize Qt Application (call this when DLL loads)
    bool initialize();
    
    // Cleanup Qt Application (call this when DLL unloads)
    void cleanup();
    
    // Check if Qt is initialized
    bool isInitialized() const;
    
    // Get the QApplication instance
    QApplication* getApplication() const;

private:
    QtApplicationManager() = default;
    ~QtApplicationManager() = default;

    // Delete copy constructor and assignment operator
    QtApplicationManager(const QtApplicationManager&) = delete;
    QtApplicationManager& operator=(const QtApplicationManager&) = delete;

    // Helper methods
    void setupPluginPaths();
    bool verifyPlatformPlugin();
    QString getDllDirectory();

    std::unique_ptr<QApplication> m_app;
    bool m_initialized = false;
    static std::mutex s_mutex;

    // Dummy argc/argv for QApplication
    int m_argc = 1;
    char m_appName[16] = "nx_plugin";
    char* m_argv[2] = {m_appName, nullptr};
};
