#include "dimension_manager.h"
#include "common_header.hpp"

// NX Open includes
#include <NXOpen/Session.hxx>
#include <NXOpen/Part.hxx>
#include <NXOpen/PartCollection.hxx>
#include <NXOpen/Annotations_Dimension.hxx>
#include <NXOpen/Annotations_DimensionCollection.hxx>
#include <NXOpen/Drawings_DrawingSheet.hxx>
#include <NXOpen/Drawings_DrawingSheetCollection.hxx>
#include <NXOpen/ListingWindow.hxx>

// Standard includes
#include <sstream>
#include <algorithm>
#include <iostream>
#include <stdexcept>

//=============================================================================
// DimensionInfo Implementation
//=============================================================================

DimensionInfo::DimensionInfo(NXOpen::Annotations::Dimension* dimension)
    : nxDimension(dimension) {
    if (dimension) {
        try {
            // Generate IDs first (these are safer operations)
            std::ostringstream idStream;
            idStream << "DIM_" << reinterpret_cast<uintptr_t>(dimension);
            dimensionId = idStream.str();

            std::ostringstream tagStream;
            tagStream << dimension->Tag();
            dimensionTag = tagStream.str();

            // Extract basic properties with error handling
            try {
                nominalValue = dimension->ComputedSize();
            } catch (...) {
                nominalValue = 0.0;
            }

            try {
                annotationOrigin = Point3D(dimension->AnnotationOrigin());
            } catch (...) {
                annotationOrigin = Point3D(0, 0, 0);
            }

            // Extract text with careful error handling
            // Use the safer utility function instead of direct call
            text = DimensionUtils::extractDimensionText(dimension);
            if (text.empty()) {
                text = std::to_string(nominalValue);
            }

            // Extract measurement direction if available
            try {
                auto measurementDir = dimension->MeasurementDirection();
                if (measurementDir) {
                    auto dirVector = measurementDir->Vector();
                    measurementDirection = Point3D(dirVector.X, dirVector.Y, dirVector.Z);
                }
            } catch (...) {
                measurementDirection = Point3D(0, 0, 0);
            }

        } catch (const std::exception& e) {
            // Log error but don't throw - allow partial initialization
            dimensionId = "INVALID_DIM";
            dimensionTag = "INVALID_TAG";
            text = "ERROR";
            nominalValue = 0.0;
        } catch (...) {
            // Catch any other exceptions
            dimensionId = "INVALID_DIM";
            dimensionTag = "INVALID_TAG";
            text = "ERROR";
            nominalValue = 0.0;
        }
    }
}

bool DimensionInfo::isValid() const {
    return nxDimension != nullptr && 
           !dimensionId.empty() && 
           dimensionId != "INVALID_DIM" &&
           !dimensionTag.empty() &&
           dimensionTag != "INVALID_TAG";
}

std::string DimensionInfo::getDisplayText() const {
    if (!text.empty()) {
        return text;
    }
    return std::to_string(nominalValue);
}

std::string DimensionInfo::getToleranceDisplayText() const {
    if (upperTolerance != 0.0 || lowerTolerance != 0.0) {
        std::ostringstream oss;
        oss << "+" << upperTolerance << "/-" << lowerTolerance;
        return oss.str();
    }
    return "None";
}

//=============================================================================
// DimensionManager Implementation
//=============================================================================

DimensionManager::DimensionManager(QObject* parent) : QObject(parent) {
}

bool DimensionManager::extractAllDimensions() {
    try {
        clearDimensions();
        
        auto* session = NXOpen::Session::GetSession();
        if (!session) {
            emit extractionError("No NX session available");
            return false;
        }
        
        auto* workPart = session->Parts()->Work();
        if (!workPart) {
            emit extractionError("No work part available");
            return false;
        }
        
        logInfo("Starting dimension extraction...");
        
        // Get all dimensions from the work part
        auto* dimensionCollection = workPart->Dimensions();
        if (!dimensionCollection) {
            emit extractionError("No dimension collection available");
            return false;
        }
        
        size_t extractedCount = 0;
        
        // Iterate through all dimensions with enhanced error handling
        for (auto it = dimensionCollection->begin(); it != dimensionCollection->end(); ++it) {
            try {
                auto* nxDimension = dynamic_cast<NXOpen::Annotations::Dimension*>(*it);
                if (nxDimension && DimensionUtils::isValidDimension(nxDimension)) {
                    DimensionInfo info;
                    if (extractDimensionFromNX(nxDimension, info)) {
                        m_dimensions.push_back(info);
                        extractedCount++;
                        logInfo("Successfully extracted dimension: " + info.dimensionId);
                    } else {
                        logWarning("Failed to extract dimension data");
                    }
                } else {
                    logWarning("Invalid dimension object encountered");
                }
            } catch (const std::exception& e) {
                logError("Exception while processing dimension: " + std::string(e.what()));
            } catch (...) {
                logError("Unknown exception while processing dimension");
            }
        }
        
        // Update internal mappings
        updateMappings();
        
        logInfo("Extracted " + std::to_string(extractedCount) + " dimensions");
        emit dimensionsExtracted(extractedCount);
        
        return extractedCount > 0;
        
    } catch (const std::exception& e) {
        std::string errorMsg = "Error extracting dimensions: " + std::string(e.what());
        logError(errorMsg);
        emit extractionError(QString::fromStdString(errorMsg));
        return false;
    }
}

void DimensionManager::clearDimensions() {
    m_dimensions.clear();
    clearMappings();
    emit dimensionsCleared();
}

bool DimensionManager::extractDimensionFromNX(NXOpen::Annotations::Dimension* nxDimension, DimensionInfo& info) {
    if (!nxDimension) {
        logWarning("Null dimension pointer passed to extractDimensionFromNX");
        return false;
    }

    try {
        // Initialize with NX dimension (this handles the problematic GetDimensionText call)
        info = DimensionInfo(nxDimension);

        // Only proceed if basic initialization succeeded
        if (!info.isValid()) {
            logWarning("Basic dimension initialization failed");
            return false;
        }

        // Extract additional properties with individual error handling
        try {
            extractDimensionProperties(nxDimension, info);
        } catch (...) {
            logWarning("Failed to extract dimension properties for " + info.dimensionId);
        }

        try {
            extractDimensionPosition(nxDimension, info);
        } catch (...) {
            logWarning("Failed to extract dimension position for " + info.dimensionId);
        }

        try {
            extractDimensionContext(nxDimension, info);
        } catch (...) {
            logWarning("Failed to extract dimension context for " + info.dimensionId);
        }

        try {
            extractToleranceInfo(nxDimension, info);
        } catch (...) {
            logWarning("Failed to extract tolerance info for " + info.dimensionId);
        }

        return info.isValid();

    } catch (const std::exception& e) {
        logError("Failed to extract dimension: " + std::string(e.what()));
        return false;
    } catch (...) {
        logError("Unknown exception in extractDimensionFromNX");
        return false;
    }
}

bool DimensionManager::extractDimensionProperties(NXOpen::Annotations::Dimension* nxDimension, DimensionInfo& info) {
    try {
        // Get measurement type
        info.measurementType = DimensionUtils::measurementTypeToString(nxDimension);
        
        // Get tolerance type
        info.toleranceType = DimensionUtils::toleranceTypeToString(nxDimension);
        
        // Extract dimension text
        info.text = DimensionUtils::extractDimensionText(nxDimension);
        
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

bool DimensionManager::extractDimensionPosition(NXOpen::Annotations::Dimension* nxDimension, DimensionInfo& info) {
    try {
        // Annotation origin is already set in constructor
        
        // Calculate zone if applicable
        info.zone = DimensionUtils::calculateZone(info.annotationOrigin);
        
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

bool DimensionManager::extractDimensionContext(NXOpen::Annotations::Dimension* nxDimension, DimensionInfo& info) {
    try {
        auto* session = NXOpen::Session::GetSession();
        auto* workPart = session->Parts()->Work();
        
        // Try to get current drawing sheet
        auto* currentSheet = workPart->DrawingSheets()->CurrentDrawingSheet();
        if (currentSheet) {
            info.sheetName = currentSheet->Name().GetText();
            
            // Get view name if available
            auto view = currentSheet->View();
            if (view) {
                info.viewName = view->Name().GetText();
            }
        }
        
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

bool DimensionManager::extractToleranceInfo(NXOpen::Annotations::Dimension* nxDimension, DimensionInfo& info) {
    try {
        // This is a simplified implementation
        // In a real application, you would extract actual tolerance values
        // from the dimension's tolerance properties
        
        info.upperTolerance = 0.0;
        info.lowerTolerance = 0.0;
        info.maximumValue = info.nominalValue + info.upperTolerance;
        info.minimumValue = info.nominalValue - info.lowerTolerance;
        
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

DimensionInfo* DimensionManager::findDimensionById(const std::string& dimensionId) {
    auto it = m_dimensionIdMap.find(dimensionId);
    if (it != m_dimensionIdMap.end() && it->second < m_dimensions.size()) {
        return &m_dimensions[it->second];
    }
    return nullptr;
}

DimensionInfo* DimensionManager::findDimensionByNXObject(NXOpen::Annotations::Dimension* dimension) {
    if (!dimension) return nullptr;
    
    for (auto& info : m_dimensions) {
        if (info.nxDimension == dimension) {
            return &info;
        }
    }
    return nullptr;
}

bool DimensionManager::assignBalloonToDimension(const std::string& dimensionId, int balloonId) {
    auto* info = findDimensionById(dimensionId);
    if (info) {
        // Remove old balloon assignment if exists
        if (info->hasBalloon) {
            m_balloonIdMap.erase(info->balloonId);
        }
        
        // Assign new balloon
        info->balloonId = balloonId;
        info->hasBalloon = true;
        m_balloonIdMap[balloonId] = dimensionId;
        
        emit balloonAssigned(QString::fromStdString(dimensionId), balloonId);
        return true;
    }
    return false;
}

bool DimensionManager::assignBalloonToDimension(NXOpen::Annotations::Dimension* dimension, int balloonId) {
    auto* info = findDimensionByNXObject(dimension);
    if (info) {
        return assignBalloonToDimension(info->dimensionId, balloonId);
    }
    return false;
}

bool DimensionManager::removeBalloonFromDimension(const std::string& dimensionId) {
    auto* info = findDimensionById(dimensionId);
    if (info && info->hasBalloon) {
        m_balloonIdMap.erase(info->balloonId);
        info->balloonId = -1;
        info->hasBalloon = false;

        emit balloonRemoved(QString::fromStdString(dimensionId));
        return true;
    }
    return false;
}

bool DimensionManager::removeBalloonFromDimension(NXOpen::Annotations::Dimension* dimension) {
    auto* info = findDimensionByNXObject(dimension);
    if (info) {
        return removeBalloonFromDimension(info->dimensionId);
    }
    return false;
}

DimensionInfo* DimensionManager::findDimensionByTag(const std::string& dimensionTag) {
    for (auto& info : m_dimensions) {
        if (info.dimensionTag == dimensionTag) {
            return &info;
        }
    }
    return nullptr;
}

std::vector<DimensionInfo*> DimensionManager::getDimensionsWithBalloons() {
    std::vector<DimensionInfo*> result;
    for (auto& info : m_dimensions) {
        if (info.hasBalloon) {
            result.push_back(&info);
        }
    }
    return result;
}

std::vector<DimensionInfo*> DimensionManager::getDimensionsWithoutBalloons() {
    std::vector<DimensionInfo*> result;
    for (auto& info : m_dimensions) {
        if (!info.hasBalloon) {
            result.push_back(&info);
        }
    }
    return result;
}

int DimensionManager::getNextAvailableBalloonId() const {
    int nextId = 1;
    while (isBalloonIdUsed(nextId)) {
        nextId++;
    }
    return nextId;
}

bool DimensionManager::isBalloonIdUsed(int balloonId) const {
    return m_balloonIdMap.find(balloonId) != m_balloonIdMap.end();
}

size_t DimensionManager::getBalloonCount() const {
    return m_balloonIdMap.size();
}

size_t DimensionManager::getUnassignedDimensionCount() const {
    size_t count = 0;
    for (const auto& info : m_dimensions) {
        if (!info.hasBalloon) {
            count++;
        }
    }
    return count;
}

bool DimensionManager::validateDimensions() {
    bool allValid = true;
    for (const auto& info : m_dimensions) {
        if (!info.isValid()) {
            allValid = false;
            logWarning("Invalid dimension found: " + info.dimensionId);
        }
    }
    return allValid;
}

bool DimensionManager::refreshFromNX() {
    return extractAllDimensions();
}

void DimensionManager::updateMappings() {
    m_dimensionIdMap.clear();
    for (size_t i = 0; i < m_dimensions.size(); ++i) {
        m_dimensionIdMap[m_dimensions[i].dimensionId] = i;
    }
}

void DimensionManager::clearMappings() {
    m_dimensionIdMap.clear();
    m_balloonIdMap.clear();
}

void DimensionManager::logError(const std::string& message) const {
    try {
        auto* session = NXOpen::Session::GetSession();
        if (session) {
            auto* lw = session->ListingWindow();
            if (lw) {
                lw->WriteLine(("ERROR: " + message).c_str());
            }
        }
    } catch (...) {
        // Ignore logging errors
    }
}

void DimensionManager::logWarning(const std::string& message) const {
    try {
        auto* session = NXOpen::Session::GetSession();
        if (session) {
            auto* lw = session->ListingWindow();
            if (lw) {
                lw->WriteLine(("WARNING: " + message).c_str());
            }
        }
    } catch (...) {
        // Ignore logging errors
    }
}

void DimensionManager::logInfo(const std::string& message) const {
    try {
        auto* session = NXOpen::Session::GetSession();
        if (session) {
            auto* lw = session->ListingWindow();
            if (lw) {
                lw->WriteLine(("INFO: " + message).c_str());
            }
        }
    } catch (...) {
        // Ignore logging errors
    }
}

//=============================================================================
// DimensionUtils Implementation
//=============================================================================

namespace DimensionUtils {

std::string measurementTypeToString(NXOpen::Annotations::Dimension* dimension) {
    if (!dimension) return "Unknown";
    
    try {
        auto measurementType = dimension->GetMeasurementType();
        // Convert enum to string - this is simplified
        return "Linear"; // Default for now
    } catch (const std::exception&) {
        return "Unknown";
    }
}

std::string toleranceTypeToString(NXOpen::Annotations::Dimension* dimension) {
    if (!dimension) return "None";
    
    // This is a simplified implementation
    return "None";
}

std::string extractDimensionText(NXOpen::Annotations::Dimension* dimension) {
    if (!dimension) return "";

    // Try multiple approaches to get dimension text

    // Approach 1: Try GetDimensionText (this is the one that crashes)
    try {
        // std::vector<NXOpen::NXString> mainTextLines, dualTextLines;
        // dimension->GetDimensionText(mainTextLines, dualTextLines);
        // if (!mainTextLines.empty()) {
        //     std::string text = mainTextLines[0].GetText();
        //     if (!text.empty()) {
        //         return text;
        //     }
        // }
        std::string name = dimension->Name().GetText();
        return name;
    }
    catch (const NXOpen::NXException& ex) {
        // Silently continue to next approach
        std::cout << "NX Exception: " << ex.Message()<< std::endl;
    }

    // Approach 2: Try to get nominal value as text
    try {
        double nominalValue = dimension->ComputedSize();
        if (nominalValue != 0.0) {
            return std::to_string(nominalValue);
        }
    } catch (...) {
        // Silently continue to next approach
    }

    // Approach 3: Try to get dimension name or tag
    try {
        tag_t tag = dimension->Tag();
        if (tag != NULL_TAG) {
            return "DIM_" + std::to_string(tag);
        }
    } catch (...) {
        // Silently continue
    }

    // Fallback: Return a default value
    return "N/A";
}

std::string calculateZone(const Point3D& position) {
    // Simplified zone calculation - could be enhanced based on drawing standards
    return "A1"; // Default zone
}

bool isValidDimension(NXOpen::Annotations::Dimension* dimension) {
    if (dimension == nullptr) {
        return false;
    }

    try {
        // Try to access basic properties to validate the dimension
        tag_t tag = dimension->Tag(); // This should not throw if the dimension is valid
        if (tag == NULL_TAG) {
            return false;
        }

        // Try to access ComputedSize as another validation
        dimension->ComputedSize();

        return true;
    } catch (...) {
        // If any exception occurs, the dimension is not valid
        return false;
    }
}

} // namespace DimensionUtils
