#include "dimension_manager.h"
#include "common_header.hpp"

// NX Open includes
#include <NXOpen/Session.hxx>
#include <NXOpen/Part.hxx>
#include <NXOpen/PartCollection.hxx>
#include <NXOpen/Annotations_Dimension.hxx>
#include <NXOpen/Annotations_DimensionCollection.hxx>
#include <NXOpen/Drawings_DrawingSheet.hxx>
#include <NXOpen/Drawings_DrawingSheetCollection.hxx>
#include <NXOpen/ListingWindow.hxx>

// Standard includes
#include <sstream>
#include <algorithm>
#include <stdexcept>

//=============================================================================
// DimensionInfo Implementation
//=============================================================================

DimensionInfo::DimensionInfo(NXOpen::Annotations::Dimension* dimension) 
    : nxDimension(dimension) {
    if (dimension) {
        try {
            // Extract basic properties
            nominalValue = dimension->ComputedSize();
            annotationOrigin = Point3D(dimension->AnnotationOrigin());
            
            // Generate IDs
            std::ostringstream idStream;
            idStream << "DIM_" << reinterpret_cast<uintptr_t>(dimension);
            dimensionId = idStream.str();
            
            std::ostringstream tagStream;
            tagStream << dimension->Tag();
            dimensionTag = tagStream.str();
            
            // Extract text
            std::vector<NXOpen::NXString> mainTextLines, dualTextLines;
            dimension->GetDimensionText(mainTextLines, dualTextLines);
            if (!mainTextLines.empty()) {
                text = mainTextLines[0].GetText();
            }
            
            // Extract measurement direction if available
            auto measurementDir = dimension->MeasurementDirection();
            if (measurementDir) {
                auto dirVector = measurementDir->Vector();
                measurementDirection = Point3D(dirVector.X, dirVector.Y, dirVector.Z);
            }
            
        } catch (const std::exception& e) {
            // Log error but don't throw - allow partial initialization
            dimensionId = "INVALID_DIM";
            dimensionTag = "INVALID_TAG";
        }
    }
}

bool DimensionInfo::isValid() const {
    return nxDimension != nullptr && 
           !dimensionId.empty() && 
           dimensionId != "INVALID_DIM" &&
           !dimensionTag.empty() &&
           dimensionTag != "INVALID_TAG";
}

std::string DimensionInfo::getDisplayText() const {
    if (!text.empty()) {
        return text;
    }
    return std::to_string(nominalValue);
}

std::string DimensionInfo::getToleranceDisplayText() const {
    if (upperTolerance != 0.0 || lowerTolerance != 0.0) {
        std::ostringstream oss;
        oss << "+" << upperTolerance << "/-" << lowerTolerance;
        return oss.str();
    }
    return "None";
}

//=============================================================================
// DimensionManager Implementation
//=============================================================================

DimensionManager::DimensionManager(QObject* parent) : QObject(parent) {
}

bool DimensionManager::extractAllDimensions() {
    try {
        clearDimensions();
        
        auto* session = NXOpen::Session::GetSession();
        if (!session) {
            emit extractionError("No NX session available");
            return false;
        }
        
        auto* workPart = session->Parts()->Work();
        if (!workPart) {
            emit extractionError("No work part available");
            return false;
        }
        
        logInfo("Starting dimension extraction...");
        
        // Get all dimensions from the work part
        auto* dimensionCollection = workPart->Dimensions();
        if (!dimensionCollection) {
            emit extractionError("No dimension collection available");
            return false;
        }
        
        size_t extractedCount = 0;
        
        // Iterate through all dimensions
        for (auto it = dimensionCollection->begin(); it != dimensionCollection->end(); ++it) {
            auto* nxDimension = dynamic_cast<NXOpen::Annotations::Dimension*>(*it);
            if (nxDimension) {
                DimensionInfo info;
                if (extractDimensionFromNX(nxDimension, info)) {
                    m_dimensions.push_back(info);
                    extractedCount++;
                }
            }
        }
        
        // Update internal mappings
        updateMappings();
        
        logInfo("Extracted " + std::to_string(extractedCount) + " dimensions");
        emit dimensionsExtracted(extractedCount);
        
        return extractedCount > 0;
        
    } catch (const std::exception& e) {
        std::string errorMsg = "Error extracting dimensions: " + std::string(e.what());
        logError(errorMsg);
        emit extractionError(QString::fromStdString(errorMsg));
        return false;
    }
}

void DimensionManager::clearDimensions() {
    m_dimensions.clear();
    clearMappings();
    emit dimensionsCleared();
}

bool DimensionManager::extractDimensionFromNX(NXOpen::Annotations::Dimension* nxDimension, DimensionInfo& info) {
    if (!nxDimension) {
        return false;
    }
    
    try {
        // Initialize with NX dimension
        info = DimensionInfo(nxDimension);
        
        // Extract additional properties
        extractDimensionProperties(nxDimension, info);
        extractDimensionPosition(nxDimension, info);
        extractDimensionContext(nxDimension, info);
        extractToleranceInfo(nxDimension, info);
        
        return info.isValid();
        
    } catch (const std::exception& e) {
        logError("Failed to extract dimension: " + std::string(e.what()));
        return false;
    }
}

bool DimensionManager::extractDimensionProperties(NXOpen::Annotations::Dimension* nxDimension, DimensionInfo& info) {
    try {
        // Get measurement type
        info.measurementType = DimensionUtils::measurementTypeToString(nxDimension);
        
        // Get tolerance type
        info.toleranceType = DimensionUtils::toleranceTypeToString(nxDimension);
        
        // Extract dimension text
        info.text = DimensionUtils::extractDimensionText(nxDimension);
        
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

bool DimensionManager::extractDimensionPosition(NXOpen::Annotations::Dimension* nxDimension, DimensionInfo& info) {
    try {
        // Annotation origin is already set in constructor
        
        // Calculate zone if applicable
        info.zone = DimensionUtils::calculateZone(info.annotationOrigin);
        
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

bool DimensionManager::extractDimensionContext(NXOpen::Annotations::Dimension* nxDimension, DimensionInfo& info) {
    try {
        auto* session = NXOpen::Session::GetSession();
        auto* workPart = session->Parts()->Work();
        
        // Try to get current drawing sheet
        auto* currentSheet = workPart->DrawingSheets()->CurrentDrawingSheet();
        if (currentSheet) {
            info.sheetName = currentSheet->Name();
            
            // Get view name if available
            auto view = currentSheet->View();
            if (view) {
                info.viewName = view->Name();
            }
        }
        
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

bool DimensionManager::extractToleranceInfo(NXOpen::Annotations::Dimension* nxDimension, DimensionInfo& info) {
    try {
        // This is a simplified implementation
        // In a real application, you would extract actual tolerance values
        // from the dimension's tolerance properties
        
        info.upperTolerance = 0.0;
        info.lowerTolerance = 0.0;
        info.maximumValue = info.nominalValue + info.upperTolerance;
        info.minimumValue = info.nominalValue - info.lowerTolerance;
        
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

DimensionInfo* DimensionManager::findDimensionById(const std::string& dimensionId) {
    auto it = m_dimensionIdMap.find(dimensionId);
    if (it != m_dimensionIdMap.end() && it->second < m_dimensions.size()) {
        return &m_dimensions[it->second];
    }
    return nullptr;
}

DimensionInfo* DimensionManager::findDimensionByNXObject(NXOpen::Annotations::Dimension* dimension) {
    if (!dimension) return nullptr;
    
    for (auto& info : m_dimensions) {
        if (info.nxDimension == dimension) {
            return &info;
        }
    }
    return nullptr;
}

bool DimensionManager::assignBalloonToDimension(const std::string& dimensionId, int balloonId) {
    auto* info = findDimensionById(dimensionId);
    if (info) {
        // Remove old balloon assignment if exists
        if (info->hasBalloon) {
            m_balloonIdMap.erase(info->balloonId);
        }
        
        // Assign new balloon
        info->balloonId = balloonId;
        info->hasBalloon = true;
        m_balloonIdMap[balloonId] = dimensionId;
        
        emit balloonAssigned(QString::fromStdString(dimensionId), balloonId);
        return true;
    }
    return false;
}

bool DimensionManager::assignBalloonToDimension(NXOpen::Annotations::Dimension* dimension, int balloonId) {
    auto* info = findDimensionByNXObject(dimension);
    if (info) {
        return assignBalloonToDimension(info->dimensionId, balloonId);
    }
    return false;
}

bool DimensionManager::removeBalloonFromDimension(const std::string& dimensionId) {
    auto* info = findDimensionById(dimensionId);
    if (info && info->hasBalloon) {
        m_balloonIdMap.erase(info->balloonId);
        info->balloonId = -1;
        info->hasBalloon = false;

        emit balloonRemoved(QString::fromStdString(dimensionId));
        return true;
    }
    return false;
}

bool DimensionManager::removeBalloonFromDimension(NXOpen::Annotations::Dimension* dimension) {
    auto* info = findDimensionByNXObject(dimension);
    if (info) {
        return removeBalloonFromDimension(info->dimensionId);
    }
    return false;
}

DimensionInfo* DimensionManager::findDimensionByTag(const std::string& dimensionTag) {
    for (auto& info : m_dimensions) {
        if (info.dimensionTag == dimensionTag) {
            return &info;
        }
    }
    return nullptr;
}

std::vector<DimensionInfo*> DimensionManager::getDimensionsWithBalloons() {
    std::vector<DimensionInfo*> result;
    for (auto& info : m_dimensions) {
        if (info.hasBalloon) {
            result.push_back(&info);
        }
    }
    return result;
}

std::vector<DimensionInfo*> DimensionManager::getDimensionsWithoutBalloons() {
    std::vector<DimensionInfo*> result;
    for (auto& info : m_dimensions) {
        if (!info.hasBalloon) {
            result.push_back(&info);
        }
    }
    return result;
}

int DimensionManager::getNextAvailableBalloonId() const {
    int nextId = 1;
    while (isBalloonIdUsed(nextId)) {
        nextId++;
    }
    return nextId;
}

bool DimensionManager::isBalloonIdUsed(int balloonId) const {
    return m_balloonIdMap.find(balloonId) != m_balloonIdMap.end();
}

size_t DimensionManager::getBalloonCount() const {
    return m_balloonIdMap.size();
}

size_t DimensionManager::getUnassignedDimensionCount() const {
    size_t count = 0;
    for (const auto& info : m_dimensions) {
        if (!info.hasBalloon) {
            count++;
        }
    }
    return count;
}

bool DimensionManager::validateDimensions() {
    bool allValid = true;
    for (const auto& info : m_dimensions) {
        if (!info.isValid()) {
            allValid = false;
            logWarning("Invalid dimension found: " + info.dimensionId);
        }
    }
    return allValid;
}

bool DimensionManager::refreshFromNX() {
    return extractAllDimensions();
}

void DimensionManager::updateMappings() {
    m_dimensionIdMap.clear();
    for (size_t i = 0; i < m_dimensions.size(); ++i) {
        m_dimensionIdMap[m_dimensions[i].dimensionId] = i;
    }
}

void DimensionManager::clearMappings() {
    m_dimensionIdMap.clear();
    m_balloonIdMap.clear();
}

void DimensionManager::logError(const std::string& message) const {
    try {
        auto* session = NXOpen::Session::GetSession();
        if (session) {
            auto* lw = session->ListingWindow();
            if (lw) {
                lw->WriteLine(("ERROR: " + message).c_str());
            }
        }
    } catch (...) {
        // Ignore logging errors
    }
}

void DimensionManager::logWarning(const std::string& message) const {
    try {
        auto* session = NXOpen::Session::GetSession();
        if (session) {
            auto* lw = session->ListingWindow();
            if (lw) {
                lw->WriteLine(("WARNING: " + message).c_str());
            }
        }
    } catch (...) {
        // Ignore logging errors
    }
}

void DimensionManager::logInfo(const std::string& message) const {
    try {
        auto* session = NXOpen::Session::GetSession();
        if (session) {
            auto* lw = session->ListingWindow();
            if (lw) {
                lw->WriteLine(("INFO: " + message).c_str());
            }
        }
    } catch (...) {
        // Ignore logging errors
    }
}

//=============================================================================
// DimensionUtils Implementation
//=============================================================================

namespace DimensionUtils {

std::string measurementTypeToString(NXOpen::Annotations::Dimension* dimension) {
    if (!dimension) return "Unknown";
    
    try {
        auto measurementType = dimension->GetMeasurementType();
        // Convert enum to string - this is simplified
        return "Linear"; // Default for now
    } catch (const std::exception&) {
        return "Unknown";
    }
}

std::string toleranceTypeToString(NXOpen::Annotations::Dimension* dimension) {
    if (!dimension) return "None";
    
    // This is a simplified implementation
    return "None";
}

std::string extractDimensionText(NXOpen::Annotations::Dimension* dimension) {
    if (!dimension) return "";
    
    try {
        std::vector<NXOpen::NXString> mainTextLines, dualTextLines;
        dimension->GetDimensionText(mainTextLines, dualTextLines);
        if (!mainTextLines.empty()) {
            return mainTextLines[0].GetText();
        }
    } catch (const std::exception&) {
        // Fall back to nominal value
    }
    
    try {
        double nominalValue = dimension->ComputedSize();
        return std::to_string(nominalValue);
    } catch (const std::exception&) {
        return "N/A";
    }
}

std::string calculateZone(const Point3D& position) {
    // Simplified zone calculation - could be enhanced based on drawing standards
    return "A1"; // Default zone
}

bool isValidDimension(NXOpen::Annotations::Dimension* dimension) {
    return dimension != nullptr;
}

} // namespace DimensionUtils
